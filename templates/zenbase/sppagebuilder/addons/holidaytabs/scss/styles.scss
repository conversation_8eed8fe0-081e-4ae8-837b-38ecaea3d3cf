.sppb-addon-holiday-tabs {
    position: relative;

    .sppb-nav-tabs {
        display: flex;
        overflow-x: auto;
        overflow-y: visible;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        -webkit-overflow-scrolling: touch;
        list-style: none;
        padding: 0;
        margin: 0 0 20px 0;
        border-bottom: 1px solid rgba(255,255,255,0.2);
        background: transparent;
        position: relative;
        z-index: 2;

        /* Hide scrollbar for Chrome, Safari and Opera */
        &::-webkit-scrollbar {
            display: none;
        }

        .sppb-tab {
            flex: 0 0 auto;
            white-space: nowrap;
        }

        > div {
            margin-bottom: -1px;
            padding: 15px 25px;
            cursor: pointer;
            color: rgba(255,255,255,0.8);
            border: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;

            &:hover {
                color: #fff;
                background-color: rgba(255,255,255,0.1);
            }

            &.active {
                color: #fff;
                font-weight: 600;

                &:after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 3px;
                    background: #e74c3c;
                }
            }
        }
    }

    .sppb-tab-content,
    .sppb-addon-content .sppb-tab-content {
        margin-top: 0;
        padding: 50px 0 30px;
        background-color: #f8f9fa;
        position: relative;
        min-height: 400px;
    }

    .sppb-tab-pane {
        position: absolute;
        left: 0;
        right: 0;
        padding-left: 15px;
        padding-right: 0;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.1s ease, visibility 0.1s ease;
        will-change: opacity, visibility;
        pointer-events: none;

        &.active {
            position: relative;
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }
    }

    // Tab icon styles
    .sppb-tab-icon {
        display: inline-block;
        margin-right: 8px;
        vertical-align: middle;

        img.svg-icon {
            width: 24px;
            height: 24px;
            vertical-align: middle;
        }

        svg {
            width: 24px;
            height: 24px;
            fill: currentColor;
            color: inherit;
        }
    }

    .sppb-tab-title {
        vertical-align: middle;
    }

    .sppb-tab.active .sppb-tab-icon svg {
        color: inherit;
        fill: currentColor;
    }
}

/* Category Header */
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 0 15px;
    gap: 2rem;

    &-content {
        min-width: 0;
    }

    &-action {
        width: fit-content;
        white-space: nowrap;

        .btn-primary {
            background: #e74c3c;
            border: none;
            padding: 12px 25px;
            border-radius: 4px;
            color: #fff;
            text-decoration: none;
            transition: background 0.3s ease;
            white-space: nowrap;

            &:hover {
                background: #d44333;
            }
        }
    }
}

.category-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin: 0 0 15px;
    color: #333;
}

.category-description {
    font-size: 1.1rem;
    color: #666;
}

/* Holiday Grid - Slick Slider Implementation */
.holiday-grid-container {
    margin: 30px auto;
    max-width: 1400px;
    position: relative;
    overflow: visible;
    padding-top: 20px;
    /* Remove left/right padding - will be handled by flexbox layout */
}

/* Flexbox wrapper for slider with navigation buttons */
.holiday-grid-wrapper {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 0 15px;
}

.holiday-grid {
    margin: 30px 0;
    position: relative;
    overflow: visible;
    flex: 1; /* Take remaining space in flexbox */

    // Slick slider overrides for holiday tabs
    &.slick-slider {
        margin: 30px 0;
        overflow: visible;

        .slick-list {
            margin: 0;
            padding: 0;
            overflow: visible;
        }

        .slick-track {
            display: flex;
            align-items: stretch;
            overflow: visible;
        }

        .slick-slide {
            padding: 0 15px;
            height: auto;

            > div {
                height: 100%;
            }
        }

        // Arrow styling - now in page flow without circles
        .slick-arrow {
            position: static; /* Remove absolute positioning */
            transform: none; /* Remove transform */
            z-index: 10;
            width: 40px;
            height: 40px;
            background: transparent; /* Remove background */
            border: none; /* Remove border */
            border-radius: 0; /* Remove circular shape */
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex !important;
            align-items: center;
            justify-content: center;
            font-size: 0;
            flex-shrink: 0; /* Prevent shrinking in flexbox */

            &:hover {
                background: transparent; /* Keep transparent on hover */
                box-shadow: none; /* Remove shadow */
                opacity: 0.7; /* Add subtle hover effect */
            }

            &:before {
                content: '';
                display: block;
                width: 24px; /* Slightly larger for better visibility */
                height: 24px;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }

            &.slick-prev:before {
                background-image: url('/templates/zenbase/icons/trips/arrow_back.svg');
            }

            &.slick-next:before {
                background-image: url('/templates/zenbase/icons/trips/arrow_forward.svg');
            }

            // Hide arrows on mobile and adjust layout
            @media (max-width: 768px) {
                display: none !important;
            }
        }
    }
}

// Responsive adjustments for flexbox layout
@media (max-width: 768px) {
    .holiday-grid-wrapper {
        display: block;
        padding: 0;
    }

    .holiday-grid {
        flex: none;
    }
}

@media (min-width: 1401px) {
    .holiday-grid-wrapper {
        padding: 0 30px;
        gap: 40px;
    }
}

@media (min-width: 1200px) and (max-width: 1400px) {
    .holiday-grid-wrapper {
        padding: 0 20px;
        gap: 35px;
    }
}

@media (min-width: 1025px) and (max-width: 1400px) {
    .holiday-grid-wrapper {
        gap: 30px;
    }
}

@media (max-width: 1199px) {
    .holiday-grid-container {
        margin: 30px -15px;
    }

    .holiday-grid-wrapper {
        padding: 0 15px;
        gap: 25px;
    }
}

@media (max-width: 1024px) {
    .holiday-grid-wrapper {
        gap: 20px;
    }
    }
}

/* Hide arrows on mobile and adjust layout */
@media (max-width: 768px) {
    .holiday-grid.slick-slider .slick-arrow {
        display: none !important;
    }

    /* Remove flexbox wrapper on mobile since no arrows */
    .holiday-grid-wrapper {
        display: block;
        padding: 0;
    }

    .holiday-grid {
        flex: none;
    }
}

/* Large screens - adjust flexbox wrapper spacing */
@media (min-width: 1401px) {
    .holiday-grid-wrapper {
        padding: 0 30px; /* Increase spacing for larger screens */
        gap: 40px; /* Larger gap between arrows and slider */
    }
}

@media (min-width: 1200px) and (max-width: 1400px) {
    .holiday-grid-wrapper {
        padding: 0 20px;
        gap: 35px;
    }
}

/* Medium to large screens */
@media (min-width: 1025px) and (max-width: 1400px) {
    .holiday-grid-wrapper {
        gap: 30px;
    }
}

@media (max-width: 1199px) {
    .holiday-grid-container {
        margin: 30px -15px;
    }

    .holiday-grid-wrapper {
        padding: 0 15px;
        gap: 25px;
    }
}

@media (max-width: 1024px) {
    .holiday-grid-wrapper {
        gap: 20px;
    }

        // Dots styling
        .slick-dots {
            bottom: -50px;
            text-align: center;
            padding: 0;
            margin: 0;
            list-style: none;
            display: flex !important;
            justify-content: center;
            gap: 8px;

            li {
                width: 12px;
                height: 12px;
                margin: 0;

                button {
                    width: 12px;
                    height: 12px;
                    padding: 0;
                    border: none;
                    border-radius: 50%;
                    background: #ccc;
                    cursor: pointer;
                    font-size: 0;
                    transition: background 0.3s ease;

                    &:hover {
                        background: #999;
                    }
                }

                &.slick-active button {
                    background: #e74c3c;
                }
            }

            // Show dots on all screen sizes
            @media (min-width: 769px) {
                display: flex !important;
            }
        }
    }

    // Fallback for non-slider (mobile)
    &:not(.slick-slider) {
        display: flex;
        gap: 30px;
        padding: 0 15px;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        align-items: stretch;

        &::-webkit-scrollbar {
            display: none;
        }
    }
}

@media (max-width: 1400px) {
    .holiday-grid {
        &:not(.slick-slider) {
            padding-right: 0;
        }
    }

    .sppb-addon-holiday-tabs .sppb-tab-pane {
        padding-right: 0;
    }
}

@media (max-width: 768px) {
    .category-header {
        flex-direction: column;
        gap: 20px;

        &-content {
            width: 100%;
        }

        &-action {
            width: 100%;
        }
    }

    .category-title {
        font-size: 2rem;
    }

    // Mobile-specific slider adjustments
    .holiday-grid {
        &.slick-slider {
            .slick-slide {
                padding: 0 10px;
            }
        }
    }
}

/* Holiday Cards */
.holiday-grid .zen-card {
    width: 100%;
    height: 100%;
}

.zen-card {
    height: 100%;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }

    &__body {
        padding: 20px;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    &__image {
        position: relative;
        margin: -20px -20px 20px;
        overflow: hidden;
        border-radius: 4px 4px 0 0;

        img {
            width: 100%;
            height: 275px;
            object-fit: cover;
            display: block;
        }

        &--max-height {
            max-height: 275px;
        }

        &-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.6);
            padding: 10px;
            color: #fff;
        }
    }

    &__info {
        margin: 15px 0;
        padding: 15px 0;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }
}

.zen-flex-end {
    margin-top: auto;
}

.zen-pill {
    display: inline-block;
    padding: 5px 10px;
    background: #f5f5f5;
    border-radius: 20px;
    font-size: 0.9em;
}

.disabled {
    text-decoration: line-through;
    opacity: 0.7;
}

.grade-image {
    height: 24px;
    width: auto;
}

/* Tab icon styles moved to main block */

/* Add transitions for height equalization */
.zen-card__image,
.zen-title,
.zen-text--subtitle,
.zen-card__info,
.zen-text--text-df,
.zen-price {
    display: block;
    min-height: 0;
    height: auto;
    transition: height 0.5s ease;
    will-change: height;
}

/* Equalizer Grid Styles */
.equalize-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    padding: 0 15px;
    margin: 0;
    width: 100%;

    .zen-card {
        display: flex;
        flex-direction: column;
        height: 100%;

        &__image,
        &__title,
        &__subtitle,
        &__info,
        &__text,
        &__price {
            height: 100%;
        }
    }
}

/* Mobile layout */
.mobile-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;

    .zen-card {
        height: auto;

        &__image,
        &__title,
        &__subtitle,
        &__info,
        &__text,
        &__price {
            height: auto !important;
        }
    }
}