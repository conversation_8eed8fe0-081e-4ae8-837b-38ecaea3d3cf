<?php

use Jo<PERSON>la\CMS\Factory;
use Joomla\CMS\Language\Text;
use Joomla\CMS\Uri\Uri;

// No direct access
defined('_JEXEC') or die('Restricted access');

jimport('mrzen.models.helper');
jimport('mrzen.models.helpers.category');
jimport('mrzen.helpers.ZenSessionHelper');
jimport('mrzen.classes.cache');
jimport('mrzen.classes.utils');
jimport('mzholidays.AvailabilityMode');
jimport('mzholidays.DurationMode');
jimport('mzholidays.traits.HolidayModel');

// These traits are imported but not directly used in this class
// They are required by the HolidayModel trait

class SppagebuilderAddonHolidaytabs extends SppagebuilderAddons {

    use HolidayModel;
    protected $addon;
    protected $item = null;

    public function __construct($addon) {
        parent::__construct($addon);
        // If addon is an array with settings, create a settings object
        if (is_array($addon) && isset($addon['settings'])) {
            $settings = new stdClass();
            foreach ($addon['settings'] as $key => $value) {
                $settings->$key = $value;
            }
            $this->addon = new stdClass();
            $this->addon->settings = $settings;
        } else {
        $this->addon = $addon;
        }
    }

    /**
     * AJAX handler for loading tab content
     */
    public static function getTabContentAjax() {
        try {
        $input = Factory::getApplication()->input;
        $category_id = $input->getInt('id', 0);
            $page_id = $input->getInt('page_id', 0);

            if (defined('JDEBUG') && JDEBUG) {
                error_log('HolidayTabs Description Debug - Loading content for category: ' . $category_id);
            }

        if (!$category_id) {
                if (defined('JDEBUG') && JDEBUG) {
                    error_log('HolidayTabs Description Debug - Invalid category ID');
                }
            return ['status' => false, 'message' => 'Invalid category ID'];
        }

            // Get the page content from the database
            $db = Factory::getDbo();
            $query = $db->getQuery(true)
                ->select('text')
                ->from('#__sppagebuilder')
                ->where('id = ' . (int)$page_id);

            $db->setQuery($query);
            $pageContent = $db->loadResult();

            if (!$pageContent) {
                if (defined('JDEBUG') && JDEBUG) {
                    error_log('HolidayTabs Description Debug - Page content not found');
                }
                return ['status' => false, 'message' => 'Page content not found'];
            }

            // Parse the JSON content
            $content = json_decode($pageContent);
            if (!$content) {
                if (defined('JDEBUG') && JDEBUG) {
                    error_log('HolidayTabs Description Debug - Invalid JSON content');
                }
                return ['status' => false, 'message' => 'Invalid page content format'];
            }

            // Find the holidaytabs addon in the content
            $addonSettings = null;
            foreach ($content as $row) {
                if (isset($row->columns)) {
                    foreach ($row->columns as $column) {
                        if (isset($column->addons)) {
                            foreach ($column->addons as $addon) {
                                if ($addon->name === 'holidaytabs') {
                                    $addonSettings = $addon->settings;
                                    if (defined('JDEBUG') && JDEBUG) {
                                        error_log('HolidayTabs Description Debug - Found addon settings: ' . json_encode($addonSettings));
                                    }
                                    break 3;
                                }
                            }
                        }
                    }
                }
            }

            if (!$addonSettings) {
                if (defined('JDEBUG') && JDEBUG) {
                    error_log('HolidayTabs Description Debug - Holiday tabs addon not found');
                }
                return ['status' => false, 'message' => 'Addon settings not found'];
            }

            // Create new instance with settings
            $addon = new self(['settings' => $addonSettings]);

            // Get the content
            $content = $addon->getTabContent($category_id);
            if (defined('JDEBUG') && JDEBUG) {
                error_log('HolidayTabs Description Debug - Content generated successfully');
            }

            return ['status' => true, 'content' => $content];

        } catch (Exception $e) {
            if (defined('JDEBUG') && JDEBUG) {
                error_log('HolidayTabs Description Debug - Error: ' . $e->getMessage());
                error_log('Stack trace: ' . $e->getTraceAsString());
            }
            return ['status' => false, 'message' => 'Error loading content: ' . $e->getMessage()];
        }
    }

    protected function getDbo() {
        return Factory::getDbo();
    }

    public function setError($error) {
        Factory::getApplication()->enqueueMessage($error->getMessage(), 'error');
    }

    /**
     * Get state property
     *
     * @param string|null $property Property name
     * @param mixed $default Default value
     * @return mixed Property value or default
     */
    public function getState($property = null, $default = null) {
        return $default;
    }

    public function render() {
        $settings = $this->addon->settings;

        // Add CSS file
        $doc = Factory::getDocument();
        $doc->addStyleSheet(Uri::base(true) . '/templates/zenbase/sppagebuilder/addons/holidaytabs/css/styles.css');
        $doc->addScript(Uri::base(true) . '/templates/zenbase/sppagebuilder/addons/holidaytabs/js/equalizer.js');
        $doc->addScript(Uri::base(true) . '/templates/zenbase/js/difficulty-modal-handler.js');

        $class = (isset($settings->class) && $settings->class) ? ' ' . $settings->class : '';
        $title = (isset($settings->title) && $settings->title) ? $settings->title : '';
        $heading_selector = (isset($settings->heading_selector) && $settings->heading_selector) ? $settings->heading_selector : 'h3';
        $categories = (isset($settings->categories) && $settings->categories) ? $settings->categories : [];

        // Output
        $output = '';
        $output .= '<div class="sppb-addon sppb-addon-holiday-tabs' . $class . '">';

        if ($title) {
            $output .= '<' . $heading_selector . ' class="sppb-addon-title">' . $title . '</' . $heading_selector . '>';
        }

        $output .= '<div class="sppb-addon-content">';

        // Only proceed if categories are configured
        if (!empty($categories)) {
            // Get the database
            $db = Factory::getDbo();

            // Start tabs container
            $output .= '<div class="sppb-tabs" data-tabs-animation="true">';
            $output .= '<div class="sppb-nav-tabs-container">';
            $output .= '<div class="sppb-nav sppb-nav-tabs" role="tablist">';

            // Tab headers
            foreach ($categories as $index => $category) {
                // Get category info if category_id exists
                $category_info = new stdClass();
                $category_info->title = '';

                if (isset($category->category_id)) {
                $query = $db->getQuery(true)
                    ->select(['title', 'description'])
                    ->from('#__categories')
                    ->where('id = ' . (int) $category->category_id);
                $db->setQuery($query);
                $category_info = $db->loadObject();
                }

                $active = ($index === 0) ? ' active' : '';

                // Check if URL override exists
                $hasUrl = isset($category->url) && !empty($category->url);

                // Start tab wrapper div (same for both types)
                $output .= '<div class="sppb-tab' . $active . '" role="tab"';
                if (!$hasUrl && isset($category->category_id)) {
                    $output .= ' data-tab="' . $category->category_id . '"';
                }
                $output .= '>';

                // If URL exists, wrap the content in a link
                if ($hasUrl) {
                    $output .= '<a href="' . $category->url . '">';
                }

                // Add icon if set
                if (isset($category->icon) && $category->icon) {
                    $output .= '<span class="sppb-tab-icon">';
                    // Get and output SVG file contents directly
                    $svgPath = JPATH_ROOT . '/' . $category->icon;
                    if (file_exists($svgPath)) {
                        $output .= file_get_contents($svgPath);
                    }
                    $output .= '</span>';
                }

                // Use custom label if set, otherwise use category title
                $tab_label = '';
                if (isset($category->label) && $category->label) {
                    $tab_label = $category->label;
                } elseif (isset($category_info->title) && $category_info->title) {
                    $tab_label = $category_info->title;
                } else {
                    $tab_label = 'Tab ' . ($index + 1);
                }

                $output .= '<span class="sppb-tab-title">' . $tab_label . '</span>';

                // Close URL link if exists
                if ($hasUrl) {
                    $output .= '</a>';
                }

                // Close tab wrapper
                $output .= '</div>';
            }

            $output .= '</div>'; // end sppb-nav-tabs
            $output .= '</div>'; // end sppb-nav-tabs-container

            // Tab contents
            $output .= '<div class="sppb-tab-content">';

            // Only load the first tab's content initially
            if (!empty($categories)) {
                $first_category = reset($categories);
                $output .= $this->getTabContent($first_category->category_id, true);

                // Add placeholder divs for other tabs
                foreach ($categories as $index => $category) {
                    if ($index > 0) {
                        $output .= '<div id="sppb-content-holiday-tab-' . $category->category_id . '" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id="' . $category->category_id . '"></div>';
                    }
                }
            }

            $output .= '</div>'; // end tab content
            $output .= '</div>'; // end sppb-tabs

            // Add tab handling script with AJAX loading
            $output .= '<script>
                jQuery(document).ready(function($) {
                    // Function to initialize slick slider
                    function initializeSlickSlider($container) {
                        var $slider = $container.find(".holiday-grid");

                        if ($slider.length && !$slider.hasClass("slick-initialized")) {
                            // Check if we are on tablet/desktop (576px and above)
                            if ($(window).width() >= 576) {
                                $slider.slick({
                                    slidesToShow: 4,
                                    slidesToScroll: 1,
                                    dots: true,
                                    arrows: true,
                                    infinite: true,
                                    responsive: [
                                        {
                                            breakpoint: 1200,
                                            settings: {
                                                slidesToShow: 3,
                                                slidesToScroll: 1
                                            }
                                        },
                                        {
                                            breakpoint: 992,
                                            settings: {
                                                slidesToShow: 2,
                                                slidesToScroll: 1
                                            }
                                        },
                                        {
                                            breakpoint: 768,
                                            settings: {
                                                slidesToShow: 2,
                                                slidesToScroll: 1,
                                                arrows: true,
                                                dots: true
                                            }
                                        },
                                        {
                                            breakpoint: 576,
                                            settings: "unslick"
                                        }
                                    ]
                                });
                            }
                        }
                    }

                    // Initialize slider for initially loaded content
                    initializeSlickSlider($(".sppb-tab-pane.active"));

                    // Handle window resize to reinitialize slider
                    $(window).on("resize", function() {
                        $(".holiday-grid").each(function() {
                            var $slider = $(this);
                            if ($(window).width() >= 576) {
                                if (!$slider.hasClass("slick-initialized")) {
                                    initializeSlickSlider($slider.closest(".sppb-tab-pane"));
                                }
                            } else {
                                if ($slider.hasClass("slick-initialized")) {
                                    $slider.slick("unslick");
                                }
                            }
                        });
                    });

                    // Tab click handler
                    $(".sppb-tab").on("click", function(e) {
                        var $this = $(this);

                        // If this tab contains a URL link, let the natural link behavior happen
                        if ($this.find("a").length > 0) {
                            return true; // Allow default link behavior
                        }

                        e.preventDefault(); // Only prevent default for non-link tabs
                        var tabId = $this.data("tab");

                        console.log("Tab clicked:", tabId);

                        if ($this.hasClass("active")) return;

                        // Update tabs immediately
                        $(".sppb-tab").removeClass("active");
                        $this.addClass("active");

                        // Get current and target panes
                        var $currentPane = $(".sppb-tab-pane.active");
                        var $targetPane = $("#sppb-content-holiday-tab-" + tabId);

                        console.log("Target pane:", $targetPane.length ? "found" : "not found");

                        // Switch panes immediately
                        $currentPane.removeClass("active in");
                        $targetPane.addClass("active in");

                        // Load content if not already loaded
                        if (!$targetPane.data("loaded")) {
                            console.log("Loading content for tab:", tabId);

                            // Show loading spinner
                            $targetPane.html(\'<div class="loading-spinner" style="padding: 40px; text-align: center;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>\');

                            // Make AJAX request
                            var request = {
                                "option": "com_sppagebuilder",
                                "task": "ajax",
                                "addon": "holidaytabs",
                                "id": tabId,
                                "page_id": ' . Factory::getApplication()->input->getInt('id', 0) . ',
                                "method": "getTabContent"
                            };

                            console.log("AJAX Request:", request);

                            $.ajax({
                                type: "POST",
                                url: "' . Uri::base(true) . '/index.php",
                                dataType: "json",
                                data: request,
                                success: function(response) {
                                    console.log("AJAX Response:", response);

                                    if (response.success && response.data && response.data.status) {
                                        $targetPane.html(response.data.content);
                                        $targetPane.data("loaded", true);
                                        console.log("Content loaded successfully");

                                        // Initialize slick slider for the loaded content
                                        initializeSlickSlider($targetPane);
                                    } else {
                                        console.error("AJAX Response Error:", response);
                                        var errorMessage = response.message || "Unable to load content";
                                        console.error("Error message:", errorMessage);
                                        $targetPane.html(\'<div class="error-message" style="padding: 20px; color: #dc3545;">Error: \' + errorMessage + \'</div>\');
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error("AJAX Error:", {
                                        status: status,
                                        error: error,
                                        response: xhr.responseText
                                    });

                                    try {
                                        var response = JSON.parse(xhr.responseText);
                                        console.error("Parsed error response:", response);
                                    } catch(e) {
                                        console.error("Raw error response:", xhr.responseText);
                                    }

                                    $targetPane.html(\'<div class="error-message" style="padding: 20px; color: #dc3545;">Error loading content. Please try again.</div>\');
                                }
                            });
                        }
                    });
                });
            </script>';
        } else {
            $output .= '<p>' . Text::_('Please select categories in the addon settings') . '</p>';
        }

        $output .= '</div>'; // end addon content
        $output .= '</div>'; // end addon

        return $output;
    }

    /**
     * Get content for a single tab
     * @param int $category_id
     * @param bool $is_active Whether this is being loaded directly (true) or via AJAX (false)
     * @return string
     */
    public function getTabContent($category_id, $is_active = false) {
        // Get category info using helper
        jimport('mrzen.models.helpers.category');
        $category = ZenModelCategoryHelper::getCategoryByID($category_id);

        error_log('HolidayTabs Description Debug - Getting content for category: ' . $category_id);
        error_log('HolidayTabs Description Debug - Category data: ' . json_encode($category));

        $output = '';

        if ($is_active) {
            $output .= '<div id="sppb-content-holiday-tab-' . $category_id . '" class="sppb-tab-pane sppb-fade active in" role="tabpanel" data-category-id="' . $category_id . '" data-loaded="true">';
        }

        // Category header section
        $output .= '<div class="category-header">';
        $output .= '<div class="category-header-content flex-grow-1 pe-4">';
        $output .= '<h2 class="category-title">' . $category->title . '</h2>';

        // Show custom description if set, otherwise use category description
        $description = '';

        if (isset($this->addon->settings->categories) && is_array($this->addon->settings->categories)) {
            foreach ($this->addon->settings->categories as $cat) {
                error_log('HolidayTabs Description Debug - Checking category: ' . $cat->category_id . ' vs ' . $category_id);
                if ($cat->category_id == $category_id && !empty($cat->description)) {
                    $description = $cat->description;
                    error_log('HolidayTabs Description Debug - Found description in addon settings: ' . $description);
                    break;
                }
            }
        }

        // if (!empty($description)) {
        //     $output .= '<div class="intro mt-2">' . $description . '</div>';
        // } else {
        //     error_log('HolidayTabs Description Debug - No description found in addon settings for category: ' . $category_id);
        // }

        $output .= '</div>';
        $output .= '<div class="category-header-action flex-shrink-0">';

        // Map full category aliases to collection parameters
        $collectionMap = [
            'bucket-list-adventures' => 'Bucket+List+Adventures',
            'closer-to-home' => 'Closer+To+Home'
        ];

        $collectionParam = isset($collectionMap[$category->alias]) ? $collectionMap[$category->alias] : str_replace(' ', '+', $category->title);
        $categoryUrl = Uri::root() . 'holidays#/holidays?FC.Collections=' . $collectionParam . '&sort=ordering%7CASC&page=1';
        $arrowIcon = file_get_contents(JPATH_BASE . '/templates/zenbase/icons/chevron-right.svg');
        $output .= '<a href="' . $categoryUrl . '" class="btn btn-primary col-12 col-md-auto">View more ' . $category->title . ' ' . $arrowIcon . '</a>';
        $output .= '</div>';
        $output .= '</div>';

        // Get holidays for this category
        $holidays = $this->getHolidaysForCategory($category_id);

        if (!empty($holidays)) {
            // Show debug info if enabled
            if (isset($this->addon->settings->debug) && $this->addon->settings->debug) {
                $output .= '<div class="holiday-debug-info" style="background-color: #f8f9fa; padding: 10px; margin-bottom: 15px; border-radius: 4px;">';
                $output .= '<h6>Debug Information</h6>';
                $output .= '<p>Category ID: ' . $category_id . '</p>';

                // Get category name
                $categoryName = '';
                $query = Factory::getDbo()->getQuery(true)
                    ->select('title')
                    ->from('#__categories')
                    ->where('id = ' . (int)$category_id);
                Factory::getDbo()->setQuery($query);
                $categoryName = Factory::getDbo()->loadResult();

                $output .= '<p>Category Name: ' . $categoryName . '</p>';

                // Check if manual ordering is enabled for this category
                $useManualOrdering = false;
                $manualHolidayIds = [];

                if (isset($this->addon->settings->categories) && is_array($this->addon->settings->categories)) {
                    foreach ($this->addon->settings->categories as $cat) {
                        if ($cat->category_id == $category_id &&
                            isset($cat->manual_ordering) &&
                            $cat->manual_ordering == 1 &&
                            isset($cat->holiday_order) &&
                            is_array($cat->holiday_order)) {

                            $useManualOrdering = true;

                            foreach ($cat->holiday_order as $orderItem) {
                                if (isset($orderItem->holiday_id) && !empty($orderItem->holiday_id)) {
                                    $manualHolidayIds[] = $orderItem->holiday_id;
                                }
                            }

                            break;
                        }
                    }
                }

                $output .= '<p>Manual Ordering: ' . ($useManualOrdering ? 'Enabled' : 'Disabled') . '</p>';

                if ($useManualOrdering) {
                    $output .= '<p>Manual Holiday IDs: ' . implode(', ', $manualHolidayIds) . '</p>';
                }

                $output .= '<p>Displayed Holidays:</p>';
                $output .= '<ol>';
                foreach ($holidays as $holiday) {
                    $name = !empty($holiday->name) ? $holiday->name : $holiday->title;
                    $output .= '<li>' . $name . ' (ID: ' . $holiday->id . ')</li>';
                }
                $output .= '</ol>';

                $output .= '</div>';
            }

            $output .= '<div class="holiday-grid-container">';
            $output .= '<div class="holiday-grid">';
            foreach ($holidays as $holiday) {
                $output .= $this->renderHolidayCard($holiday);
            }
            $output .= '</div>';
            $output .= '</div>'; // end holiday-grid-container
        } else {
            $output .= '<p>' . Text::_('No holidays in this category') . '</p>';
        }

        // Only close the wrapper div if this is direct content
        if ($is_active) {
            $output .= '</div>';
        }

        return $output;
    }

    /**
     * Get holidays for a specific category
     * @param int $category_id
     * @return array
     */
    private function getHolidaysForCategory($category_id) {
        // Check if manual ordering is enabled for this category
        $useManualOrdering = false;
        $manualHolidayIds = [];

        if (isset($this->addon->settings->categories) && is_array($this->addon->settings->categories)) {
            foreach ($this->addon->settings->categories as $cat) {
                if ($cat->category_id == $category_id &&
                    isset($cat->manual_ordering) &&
                    $cat->manual_ordering == 1 &&
                    isset($cat->holiday_order) &&
                    is_array($cat->holiday_order) &&
                    !empty($cat->holiday_order)) {

                    $useManualOrdering = true;

                    // Extract holiday IDs in the specified order
                    foreach ($cat->holiday_order as $orderItem) {
                        if (isset($orderItem->holiday_id) && !empty($orderItem->holiday_id)) {
                            $manualHolidayIds[] = $orderItem->holiday_id;
                        }
                    }

                    break;
                }
            }
        }

        $holidays = [];

        if ($useManualOrdering && !empty($manualHolidayIds)) {
            // Use manually specified holidays in the given order
            foreach ($manualHolidayIds as $id) {
                $holiday = $this->getItem($id);
                if ($holiday) {
                    $holidays[] = $holiday;
                }
            }
        } else {
            // Use default ordering from database
            $db = Factory::getDbo();
            $query = $db->getQuery(true);

            $query->select('id')
                ->from('#__zenholidays')
                ->where('state = 1')
                ->where('FIND_IN_SET(' . (int)$category_id . ', categories)')
                ->order('ordering ASC')
                ->setLimit(4);

            $db->setQuery($query);
            $ids = $db->loadColumn();

            foreach ($ids as $id) {
                $holiday = $this->getItem($id);
                if ($holiday) {
                    $holidays[] = $holiday;
                }
            }
        }

        return $holidays;
    }

    /**
     * Render a holiday card using the template
     *
     * @param object $holiday The holiday object to render
     * @return string The rendered HTML
     */
    private function renderHolidayCard($holiday) {
        // Get the application and template path
        $app = Factory::getApplication();
        $template = JPATH_ROOT . '/templates/' . $app->getTemplate() . '/sppagebuilder/addons/holiday/tmpl.php';

        if (!file_exists($template)) {
            // Fallback rendering if template doesn't exist
            return '<div class="alert alert-warning">Please create a holiday template at: ' . $template . '</div>';
        }

        // Start output buffering
        ob_start();
        // The $holiday variable is used inside the included template
        include($template);
        $output = ob_get_contents();
        ob_end_clean();

        return $output;
    }
}